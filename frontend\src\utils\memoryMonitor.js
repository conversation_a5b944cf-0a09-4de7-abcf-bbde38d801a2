// Memory monitoring utility for development
// Helps detect memory leaks and monitor performance

class MemoryMonitor {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development';
    this.measurements = [];
    this.maxMeasurements = 100; // Keep last 100 measurements
    this.intervalId = null;
    this.thresholds = {
      warning: 100 * 1024 * 1024, // 100MB
      critical: 200 * 1024 * 1024, // 200MB
    };
  }

  start(intervalMs = 30000) { // Default: every 30 seconds
    if (!this.isEnabled || this.intervalId) return;

    console.log('🔍 Memory Monitor: Starting memory monitoring...');
    
    this.intervalId = setInterval(() => {
      this.takeMeasurement();
    }, intervalMs);

    // Take initial measurement
    this.takeMeasurement();
  }

  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('🔍 Memory Monitor: Stopped memory monitoring');
    }
  }

  takeMeasurement() {
    if (!this.isEnabled) return;

    try {
      const measurement = {
        timestamp: Date.now(),
        memory: this.getMemoryInfo(),
        dom: this.getDOMInfo(),
        storage: this.getStorageInfo(),
        websockets: this.getWebSocketInfo(),
      };

      this.measurements.push(measurement);
      
      // Keep only the last N measurements
      if (this.measurements.length > this.maxMeasurements) {
        this.measurements = this.measurements.slice(-this.maxMeasurements);
      }

      this.checkThresholds(measurement);
      
      return measurement;
    } catch (error) {
      console.error('🔍 Memory Monitor: Error taking measurement:', error);
    }
  }

  getMemoryInfo() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        percentage: Math.round((performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100),
      };
    }
    return null;
  }

  getDOMInfo() {
    return {
      elements: document.querySelectorAll('*').length,
      eventListeners: this.countEventListeners(),
      tooltips: document.querySelectorAll('#g6-device-tooltip').length,
    };
  }

  getStorageInfo() {
    const getStorageSize = (storage) => {
      try {
        let total = 0;
        for (let key in storage) {
          if (storage.hasOwnProperty(key)) {
            total += storage[key].length + key.length;
          }
        }
        return total;
      } catch (e) {
        return 0;
      }
    };

    return {
      localStorage: getStorageSize(localStorage),
      sessionStorage: getStorageSize(sessionStorage),
    };
  }

  getWebSocketInfo() {
    // Count WebSocket connections (this is approximate)
    const wsConnections = window.WebSocket ? 1 : 0; // Basic check
    return {
      connections: wsConnections,
    };
  }

  countEventListeners() {
    // This is a rough estimate - actual event listener counting is complex
    const elements = document.querySelectorAll('*');
    let count = 0;
    
    // Check for common event attributes
    elements.forEach(el => {
      const attributes = el.attributes;
      for (let i = 0; i < attributes.length; i++) {
        if (attributes[i].name.startsWith('on')) {
          count++;
        }
      }
    });
    
    return count;
  }

  checkThresholds(measurement) {
    if (!measurement.memory) return;

    const used = measurement.memory.used;
    
    if (used > this.thresholds.critical) {
      console.error('🚨 Memory Monitor: CRITICAL - Memory usage is very high!', {
        used: this.formatBytes(used),
        percentage: measurement.memory.percentage + '%',
        measurement,
      });
      this.suggestCleanup();
    } else if (used > this.thresholds.warning) {
      console.warn('⚠️ Memory Monitor: WARNING - Memory usage is high', {
        used: this.formatBytes(used),
        percentage: measurement.memory.percentage + '%',
      });
    }
  }

  suggestCleanup() {
    console.log('💡 Memory Monitor: Cleanup suggestions:');
    console.log('  - Check for memory leaks in G6 graphs');
    console.log('  - Clear old Redux store data');
    console.log('  - Remove unused event listeners');
    console.log('  - Clear browser caches');
    
    // Trigger automatic cleanup if available
    if (window.gc && typeof window.gc === 'function') {
      console.log('🧹 Memory Monitor: Triggering garbage collection...');
      window.gc();
    }
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getReport() {
    if (!this.isEnabled || this.measurements.length === 0) {
      return 'Memory monitoring not available or no measurements taken';
    }

    const latest = this.measurements[this.measurements.length - 1];
    const oldest = this.measurements[0];
    
    const report = {
      latest: latest,
      trend: this.calculateTrend(),
      summary: {
        measurements: this.measurements.length,
        timespan: latest.timestamp - oldest.timestamp,
        averageMemory: this.calculateAverage('memory.used'),
        peakMemory: this.calculatePeak('memory.used'),
      }
    };

    console.table(report.summary);
    return report;
  }

  calculateTrend() {
    if (this.measurements.length < 2) return 'insufficient data';
    
    const recent = this.measurements.slice(-10); // Last 10 measurements
    const first = recent[0];
    const last = recent[recent.length - 1];
    
    if (!first.memory || !last.memory) return 'no memory data';
    
    const change = last.memory.used - first.memory.used;
    const changePercent = (change / first.memory.used) * 100;
    
    if (Math.abs(changePercent) < 5) return 'stable';
    return changePercent > 0 ? `increasing (+${changePercent.toFixed(1)}%)` : `decreasing (${changePercent.toFixed(1)}%)`;
  }

  calculateAverage(path) {
    const values = this.measurements
      .map(m => this.getNestedValue(m, path))
      .filter(v => v !== null && v !== undefined);
    
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
  }

  calculatePeak(path) {
    const values = this.measurements
      .map(m => this.getNestedValue(m, path))
      .filter(v => v !== null && v !== undefined);
    
    return values.length > 0 ? Math.max(...values) : 0;
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  // Manual cleanup trigger
  triggerCleanup() {
    console.log('🧹 Memory Monitor: Manual cleanup triggered');
    
    // Clean up tooltips
    const tooltips = document.querySelectorAll('#g6-device-tooltip');
    tooltips.forEach(tooltip => {
      if (document.body.contains(tooltip)) {
        document.body.removeChild(tooltip);
      }
    });
    
    // Suggest garbage collection
    if (window.gc && typeof window.gc === 'function') {
      window.gc();
    }
    
    console.log('🧹 Memory Monitor: Cleanup completed');
  }
}

// Create singleton instance
const memoryMonitor = new MemoryMonitor();

// Auto-start in development
if (process.env.NODE_ENV === 'development') {
  // Start monitoring after a short delay to let the app initialize
  setTimeout(() => {
    memoryMonitor.start();
  }, 5000);
  
  // Add global access for debugging
  window.memoryMonitor = memoryMonitor;
  
  console.log('🔍 Memory Monitor: Available globally as window.memoryMonitor');
  console.log('🔍 Commands: start(), stop(), getReport(), triggerCleanup()');
}

export default memoryMonitor;
