// Service Worker utilities to handle registration and cleanup

/**
 * Unregister all service workers to prevent async message handling errors
 */
export const unregisterAllServiceWorkers = async () => {
  if ("serviceWorker" in navigator) {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();

      for (const registration of registrations) {
        console.log("Unregistering service worker:", registration.scope);
        await registration.unregister();
      }

      console.log("All service workers unregistered successfully");
      return true;
    } catch (error) {
      console.warn("Error unregistering service workers:", error);
      return false;
    }
  }
  return false;
};

/**
 * Clear all caches to prevent stale data issues
 */
export const clearAllCaches = async () => {
  if ("caches" in window) {
    try {
      const cacheNames = await caches.keys();

      await Promise.all(
        cacheNames.map((cacheName) => {
          console.log("Deleting cache:", cacheName);
          return caches.delete(cacheName);
        })
      );

      console.log("All caches cleared successfully");
      return true;
    } catch (error) {
      console.warn("Error clearing caches:", error);
      return false;
    }
  }
  return false;
};

/**
 * Complete cleanup of service workers and caches
 */
export const cleanupServiceWorkers = async () => {
  console.log("Starting service worker cleanup...");

  const swUnregistered = await unregisterAllServiceWorkers();
  const cachesCleared = await clearAllCaches();

  if (swUnregistered || cachesCleared) {
    console.log(
      "Service worker cleanup completed. Consider refreshing the page."
    );
  }

  return { swUnregistered, cachesCleared };
};

/**
 * Check for browser extensions that might cause message channel errors
 */
export const checkForProblematicExtensions = () => {
  const extensions = [];

  // Check for React DevTools
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    extensions.push("React DevTools");
  }

  // Check for Redux DevTools
  if (window.__REDUX_DEVTOOLS_EXTENSION__) {
    extensions.push("Redux DevTools");
  }

  // Check for Chrome extension APIs
  if (typeof chrome !== "undefined" && chrome.runtime) {
    extensions.push("Chrome Extension APIs detected");
  }

  if (extensions.length > 0) {
    console.log("🔍 Browser extensions detected:", extensions);
    console.log("🔍 These might be causing the message channel error");
  }

  return extensions;
};

/**
 * Add global error handlers for service worker related errors
 */
export const addServiceWorkerErrorHandlers = () => {
  // Check for problematic extensions first
  checkForProblematicExtensions();

  // Handle service worker errors
  if ("serviceWorker" in navigator) {
    navigator.serviceWorker.addEventListener("error", (event) => {
      console.warn("Service Worker error:", event.error);
    });

    navigator.serviceWorker.addEventListener("message", (event) => {
      try {
        console.log("Received message from service worker:", event.data);
      } catch (error) {
        console.warn("Error handling service worker message:", error);
      }
    });
  }

  // Handle unhandled promise rejections that might be related to service workers
  window.addEventListener("unhandledrejection", (event) => {
    const errorMessage =
      event.reason?.message || event.reason?.toString() || "";

    if (
      errorMessage.includes("message channel closed") ||
      errorMessage.includes("A listener indicated an asynchronous response") ||
      errorMessage.includes("Extension context invalidated") ||
      errorMessage.includes("Receiving end does not exist") ||
      errorMessage.includes("Could not establish connection")
    ) {
      console.warn(
        "🔍 Suppressed extension/service worker error:",
        errorMessage
      );
      event.preventDefault();
    }
  });

  // Handle Chrome extension message errors specifically
  window.addEventListener("error", (event) => {
    const errorMessage = event.error?.message || event.message || "";

    if (
      errorMessage.includes("A listener indicated an asynchronous response") ||
      errorMessage.includes("message channel closed") ||
      errorMessage.includes("Extension context invalidated") ||
      errorMessage.includes("chrome-extension://") ||
      errorMessage.includes("Receiving end does not exist")
    ) {
      console.warn("🔍 Suppressed extension error:", errorMessage);
      event.preventDefault();
    }
  });

  // Override console.error to catch specific extension errors
  const originalConsoleError = console.error;
  console.error = function (...args) {
    const errorMessage = args.join(" ");

    if (
      errorMessage.includes(
        "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
      )
    ) {
      console.warn("🔍 Suppressed browser extension error (message channel)");
      return;
    }

    if (
      errorMessage.includes("Extension context invalidated") ||
      errorMessage.includes("chrome-extension://") ||
      errorMessage.includes("Receiving end does not exist")
    ) {
      console.warn("🔍 Suppressed browser extension error:", errorMessage);
      return;
    }

    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };
};
