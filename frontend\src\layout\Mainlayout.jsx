import { PageContainer, ProLayout } from "@ant-design/pro-components";
import _DefaultProps from "./_DefaultProps";
import useWebSocket from "react-use-websocket";
import { useEffect, useState, useCallback } from "react";
import { useTheme, useThemeMode } from "antd-style";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { App, Dropdown, Flex, Spin, Typography } from "antd";
import {
  InfoCircleOutlined,
  LogoutOutlined,
  MacCommandOutlined,
} from "@ant-design/icons";
import atopLogo from "../assets/images/NIMBL_Logo.svg";
import atopDarklogo from "../assets/images/darkmode-logo.svg";
import ThemeController from "../utils/themes/ThemeController";
import SettingsComp from "../components/comman/SettingsComp";
import MemoryMonitorWidget from "../components/dev/MemoryMonitorWidget";
import { useDispatch, useSelector } from "react-redux";
import { logoutUser } from "../features/auth/userAuthSlice";
import { useThemeStore } from "../utils/themes/useStore";
import { eventLogSelector } from "../features/eventLog/eventLogSlice";
import {
  extractSocketResult,
  socketControlSelector,
  cleanupOldSocketMessages,
} from "../features/socketControl/socketControlSlice";
import SyslogSettingDrawer from "../components/drawer/SyslogSettingDrawer";
import TrapSettingDrawer from "../components/drawer/TrapSettingDrawer";
import FirmwareDrawer from "../components/drawer/FirmwareDrawer";
import SaveRuunningConfigDrawer from "../components/drawer/SaveRuunningConfigDrawer";
import NetworkSettingDrawer from "../components/drawer/NetworkSettingDrawer";

import LicenseAlert from "../components/comman/LicenseAlert";
import ServerStatus from "../components/comman/ServerStatus";
import { ErrorBoundries } from "./FallbackErrorBoundry";
import { licenseAlertSelector } from "../features/socketControl/licenseAlertSlice";
import defaultAvatar from "../assets/images/defaultAvatar.webp";
import { RootClusterInfo } from "../features/clusterInfo/clusterInfoSlice";
import {
  layoutSliceSelector,
  setIsCollapsed,
} from "../features/comman/layoutSlice";
import { FloatButton } from "antd/lib";
import CommandModal from "../components/comman/CommandModal";
import packageInfo from "../../package.json";

const { Text } = Typography;

const MainLayout = () => {
  const { mode, wsURL } = useThemeStore();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openCmdModal, setOpenCmdModal] = useState(false);
  const token = useTheme();
  const { appearance } = useThemeMode();
  let location = useLocation();
  const [pathname, setPathname] = useState(location.pathname);
  const { notification, modal, message } = App.useApp();
  const { firmwareNotification } = useSelector(eventLogSelector);
  const { socketLoading } = useSelector(socketControlSelector);
  const { isCollapsed } = useSelector(layoutSliceSelector);
  const { showLicenseWaterMark, featureEnabled } =
    useSelector(licenseAlertSelector);

  const { lastMessage, readyState } = useWebSocket(`${wsURL}/api/v1/ws`, {
    onOpen: () => {
      console.log("Socket connection established.");
    },
    onClose: (event) => {
      console.log("Socket connection closed:", event.code, event.reason);
    },
    onError: (event) => {
      console.error("Socket connection error:", event);
    },
    shouldReconnect: (closeEvent) => {
      // Only reconnect if it's not a manual close (code 1000) or auth failure (code 1008)
      return closeEvent.code !== 1000 && closeEvent.code !== 1008;
    },
    reconnectAttempts: 10,
    reconnectInterval: 3000,
    // Add heartbeat to keep connection alive
    heartbeat: {
      message: JSON.stringify({ type: "ping" }),
      returnMessage: JSON.stringify({ type: "pong" }),
      timeout: 60000,
      interval: 25000,
    },
  });

  useEffect(() => {
    setPathname(location.pathname || "/");
  }, [location]);

  useEffect(() => {
    if (firmwareNotification !== "") {
      const splitMsg = firmwareNotification.split("firmware:");
      notification.info({
        message: `Firmware progress`,
        description: splitMsg[1],
        placement: "topRight",
      });
    }
  }, [firmwareNotification]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleDevicePolling = (msg) => {
    if (msg.includes("Devices online:")) {
      message.success({
        content: msg,
        duration: 3,
      });
    } else if (msg.includes("Devices offline:")) {
      message.error({
        content: msg,
        duration: 3,
      });
    } else {
      message.info({
        content: msg,
        duration: 3,
      });
    }
  };

  // Use useCallback to prevent unnecessary re-renders
  const processSocketMessage = useCallback(
    (messageData) => {
      try {
        // Skip ping/pong messages
        const parsed = JSON.parse(messageData);
        if (parsed.type === "ping" || parsed.type === "pong") {
          return;
        }
      } catch (e) {
        // Continue processing if not JSON
      }

      dispatch(extractSocketResult(messageData, handleDevicePolling));
    },
    [dispatch, handleDevicePolling]
  );

  useEffect(() => {
    if (lastMessage !== null && lastMessage.data) {
      // Use requestIdleCallback if available, otherwise requestAnimationFrame
      if (window.requestIdleCallback) {
        window.requestIdleCallback(
          () => {
            processSocketMessage(lastMessage.data);
          },
          { timeout: 1000 }
        );
      } else {
        requestAnimationFrame(() => {
          processSocketMessage(lastMessage.data);
        });
      }
    }
  }, [lastMessage, processSocketMessage]);

  const handleMenuClick = (e) => {
    if (e.key === "logout") {
      sessionStorage.removeItem("nmstoken");
      sessionStorage.removeItem("nmsuser");
      sessionStorage.removeItem("nmsuserrole");
      sessionStorage.removeItem("prevTopologyNodesData");
      sessionStorage.removeItem("qrcodeurl");
      sessionStorage.removeItem("sessionid");
      sessionStorage.removeItem("is2faenabled");
      dispatch(logoutUser());
      navigate("/login");
    }
  };
  const handleCollapseChange = (collapsed) => {
    dispatch(setIsCollapsed(collapsed));
  };
  useEffect(() => {
    dispatch(setIsCollapsed(true));
    // load root information on mount first time
    dispatch(RootClusterInfo({}));

    // Set up periodic cleanup of old socket messages (every 30 minutes)
    const cleanupInterval = setInterval(() => {
      dispatch(cleanupOldSocketMessages());
    }, 30 * 60 * 1000);

    return () => {
      clearInterval(cleanupInterval);
    };
  }, [dispatch]);

  const handleAboutClick = () => {
    modal.info({
      icon: null,
      width: 360,
      className: "confirm-class",
      content: (
        <Flex align="center" vertical>
          <InfoCircleOutlined
            style={{
              color: token.colorInfo,
              fontSize: 64,
            }}
          />
          <Typography.Title level={4}>
            {packageInfo?.name?.replace(/_/g, " ")}
          </Typography.Title>
          <Text strong>{packageInfo?.version}</Text>
          <Text strong>&#169; 2025 - BlackBear TechHive</Text>
        </Flex>
      ),
    });
  };

  const loggedinUser = sessionStorage.getItem("nmsuser")
    ? sessionStorage.getItem("nmsuser")
    : "admin";

  return (
    <Spin tip="Loading" size="small" spinning={socketLoading}>
      <ProLayout
        collapsed={isCollapsed}
        onCollapse={handleCollapseChange}
        {..._DefaultProps({
          anomalies: featureEnabled.includes("anomalies"),
          idps: featureEnabled.includes("idps"),
        })}
        waterMarkProps={
          showLicenseWaterMark
            ? {
                content: "NIMBL Missing Valid License !",
              }
            : undefined
        }
        siderWidth={220}
        layout="mix"
        fixSiderbar
        fixedHeader
        hasSiderMenu={true}
        siderMenuType="sub"
        ErrorBoundary={ErrorBoundries}
        menu={{
          collapsedShowGroupTitle: false,
        }}
        location={{
          pathname,
        }}
        logo={
          <img
            src={appearance === "dark" ? atopDarklogo : atopLogo}
            alt="BlackBear TechHive"
            style={{ height: "50px" }}
          />
        }
        title="BlackBear TechHive"
        headerTitleRender={(logo) => (
          <a
            target="_blank"
            href="https://blackbeartechhive.com"
            rel="noreferrer"
          >
            {logo}
          </a>
        )}
        avatarProps={{
          src: defaultAvatar,
          size: "default",
          title: loggedinUser,
          alt: "Avatar",
          render: (props, dom) => {
            return (
              <Dropdown
                trigger={["click"]}
                placement="bottom"
                arrow
                menu={{
                  items: [
                    {
                      key: "logout",
                      icon: <LogoutOutlined />,
                      label: "Logout",
                    },
                  ],
                  onClick: handleMenuClick,
                }}
              >
                {dom}
              </Dropdown>
            );
          },
        }}
        actionsRender={(props) => [
          <ServerStatus isDashboardPage={true} />,
          process.env.NODE_ENV === "development" && <SettingsComp />,
          <ThemeController />,
        ]}
        menuItemRender={(item, dom) => <Link to={item.path || "/"}>{dom}</Link>}
        menuFooterRender={() => {
          return (
            <div
              style={{
                padding: "12px 16px",
                borderTop: `1px solid ${token.colorSplit}`,
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                color: token.colorTextSecondary,
                background: token.colorBgContainer,
              }}
              onClick={handleAboutClick}
            >
              <InfoCircleOutlined />
            </div>
          );
        }}
        token={{
          sider: {
            colorBgMenuItemCollapsedElevated: token.colorBgContainer,
            colorMenuBackground: token.colorBgContainer,
            colorBgMenuItemSelected:
              mode === "dark" ? token.colorPrimary : token.colorPrimaryBg,
            colorTextMenuSelected:
              mode === "dark" ? token.colorText : token.colorPrimary,
          },
          pageContainer: {
            paddingBlockPageContainerContent: 0,
            paddingInlinePageContainerContent: 16,
          },
        }}
      >
        <PageContainer
          header={{
            title: "",
            breadcrumb: [],
          }}
        >
          <div style={{ paddingBlock: "16px" }}>
            <Outlet />
          </div>

          <FloatButton.Group
            shape="circle"
            style={{
              insetInlineEnd: 10,
              insetBlockEnd: 24,
            }}
          >
            <FloatButton
              tooltip="commands"
              type="primary"
              icon={<MacCommandOutlined />}
              onClick={() => setOpenCmdModal(true)}
            />
          </FloatButton.Group>
          <CommandModal
            open={openCmdModal}
            onCancel={() => setOpenCmdModal(false)}
          />
          <NetworkSettingDrawer />

          <SyslogSettingDrawer />
          <TrapSettingDrawer />
          <FirmwareDrawer />
          <SaveRuunningConfigDrawer />
          <LicenseAlert />
          <MemoryMonitorWidget />
        </PageContainer>
      </ProLayout>
    </Spin>
  );
};

export default MainLayout;
